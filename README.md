# WAIC Demo - Defake深伪检测防御演示项目

## 项目概述

本项目为人工智能大会（WAIC）演示项目，目标是展现Defake深伪检测防御AIGC生成人脸的效果。通过现场互动演示，让观众直观了解深伪技术的生成过程以及相应的检测防御能力。

## 整体需求设计

### 1. 展示场景定义

**展会现场设置：**
- 一台大屏幕显示器（用于展示整个演示过程）
- 现场提供预装演示APP的手机供用户采集人脸
- 现场工作人员引导用户体验

**演示流程：**
1. 用户打开Android APP拍摄人脸
2. 照片上传到系统
3. 大屏幕实时显示处理进度
4. 展示LivePortrait生成的人脸驱动视频
5. 展示Defake检测结果
6. 展示AI大模型的可解释性分析

### 2. 功能模块需求

#### 2.1 Android APP（界面设计为主）
**主要功能：**
- 简洁的拍照界面设计
- 照片预览和确认功能
- 文件上传进度显示
- 状态提示和用户引导

**UI设计要求：**
- Material Design设计风格
- 大按钮设计，适合展会现场操作
- 清晰的状态反馈
- 友好的用户提示文案

#### 2.2 Web前端展示页面（大屏幕显示）
**主要功能：**
- 实时状态显示（等待上传、处理中、完成）
- 原始照片展示区域
- 合成视频播放区域
- Defake检测结果展示
- AI大模型解释文本展示

**UI设计要求：**
- 适配大屏幕显示器的布局
- 科技感强的视觉设计
- 清晰的信息层次结构
- 流畅的状态转换动画

#### 2.3 后端处理系统
**核心功能：**
- 照片接收和预处理
- LivePortrait人脸驱动调用
- Defake检测算法集成
- 大模型API调用
- 实时状态推送

### 3. 技术实现范围

**当前阶段实现：**
- 步骤1-3：用户上传 → LivePortrait人脸驱动 → 效果展示
- 使用KwaiVGI的LivePortrait项目代码

**后续扩展：**
- 步骤4-6：Defake检测 → 结果展示 → AI解释分析

### 4. 用户体验流程

#### 4.1 用户操作流程
1. **引导阶段** - 工作人员介绍演示内容
2. **拍照阶段** - 用户使用APP拍摄自拍照
3. **上传阶段** - 照片上传，显示进度
4. **处理阶段** - 大屏显示处理进度
5. **展示阶段** - 播放生成的人脸驱动视频
6. **检测阶段** - 展示深伪检测结果
7. **解释阶段** - AI分析检测依据

#### 4.2 异常处理流程
- 网络连接异常处理
- 人脸检测失败处理
- 模型处理超时处理
- 系统错误友好提示

### 5. 性能和体验要求

**响应时间要求：**
- 照片上传：< 5秒
- LivePortrait处理：< 30秒
- Defake检测：< 15秒
- 整体流程：< 60秒

**用户体验要求：**
- 界面操作简单直观
- 状态反馈及时准确
- 错误提示友好明确
- 视觉效果震撼有趣

### 6. 展示效果目标

**技术展示目标：**
- 展现LivePortrait人脸驱动技术的先进性
- 证明Defake检测技术的有效性
- 体现AI技术在内容安全领域的应用价值

**观众体验目标：**
- 让观众直观感受深伪技术的真实效果
- 提升对AI内容安全技术的认知
- 增强对技术发展的信心和期待

## UI设计方案

### 1. Android APP界面设计

#### 主界面布局
```
┌─────────────────────────┐
│  SSID - Defake演示      │
│    深伪检测防御         │
├─────────────────────────┤
│                         │
│    [相机预览区域]        │
│                         │
│                         │
├─────────────────────────┤
│     [拍照按钮]          │
│                         │
│  [重新拍照] [确认上传]   │
│                         │
│   上传进度: ████░░ 80%   │
└─────────────────────────┘
```

#### 界面状态流转
1. **初始状态** - 显示相机预览，拍照按钮
2. **拍照完成** - 显示照片预览，重拍/确认按钮
3. **上传中** - 显示上传进度条，禁用操作
4. **上传完成** - 显示成功提示，引导用户关注大屏

#### 设计特点
- Material Design设计风格
- 大按钮设计，适合展会现场操作
- 清晰的状态反馈和用户引导
- 友好的错误提示和异常处理

### 2. 大屏幕展示界面设计

#### 整体布局架构
```
┌─────────────────────────────────────────────────────────────┐
│              SSID - Defake深伪检测防御演示                  │
├─────────────┬─────────────────────────┬─────────────────────┤
│   左侧栏    │        中央视频区        │      右侧栏         │
│  (25%)      │        (50%)           │     (25%)          │
│             │                        │                    │
│ 人脸采集     │    ┌─────────────┐      │ 深伪检测结果        │
│ ┌─────────┐ │    │             │      │ ┌─────────────┐    │
│ │  照片   │ │    │  360×640    │      │ │ 95.7% 合成  │    │
│ └─────────┘ │    │  竖屏视频    │      │ └─────────────┘    │
│             │    │             │      │                    │
│ 处理状态     │    └─────────────┘      │ 处理进度            │
│ • 人脸检测   │                        │ • 人脸采集完成      │
│ • 特征提取   │                        │ • 正在生成视频      │
│ • 视频生成   │                        │ • 深伪检测          │
│ • 深伪检测   │                        │ • AI分析           │
│             │                        │                    │
│             │                        │ 大模型AI分析        │
│             │                        │ ┌─────────────┐    │
│             │                        │ │ 基于深度学习  │    │
│             │                        │ │ 模型分析...  │    │
│             │                        │ └─────────────┘    │
└─────────────┴─────────────────────────┴─────────────────────┘
```

#### 核心设计特点

**视觉风格：**
- 深色科技风格背景，适合大屏幕展示
- 蓝色到青色的科技感渐变色彩
- 发光效果和脉冲动画增强视觉冲击
- 卡片式布局，层次分明

**视频展示区域：**
- 手机竖屏比例：360px × 640px (9:16)
- 居中显示，突出核心内容
- 扫描线动画效果，增强科技感
- 支持视频播放控制和下载功能

**状态反馈系统：**
- 实时状态指示器（等待/处理中/完成/错误）
- 进度条动画显示处理进度
- 时间线展示完整流程步骤
- 状态变化的平滑过渡动画

#### 功能模块详细设计

**左侧栏 - 输入状态区：**
- 人脸采集状态卡片：显示上传的照片和状态
- 处理状态卡片：显示各个AI处理步骤的实时进度
- 紧凑的垂直布局，信息密度适中

**中央区域 - 视频播放区：**
- 专门的手机竖屏视频容器
- 视频生成过程的可视化反馈
- 播放控制和下载功能按钮
- 加载动画和扫描线效果

**右侧栏 - 结果展示区：**
- 深伪检测结果：显示检测置信度和结论
- 处理进度时间线：显示各步骤完成状态和时间
- 大模型AI分析：滚动文本框显示详细分析结果

### 3. 交互设计规范

#### 动画效果
- **状态转换**：平滑的淡入淡出效果
- **进度指示**：脉冲动画和进度条更新
- **加载状态**：旋转图标和呼吸灯效果
- **文本显示**：打字机效果逐字显示AI分析

#### 响应式设计
- 适配不同尺寸的展示屏幕
- 保持关键信息的可读性
- 合理的字体大小和间距
- 触摸友好的按钮尺寸

#### 错误处理
- 网络连接异常的友好提示
- 人脸检测失败的重试机制
- 处理超时的状态显示
- 系统错误的用户引导

### 4. 技术实现细节

#### 前端技术栈
- HTML5 + CSS3 + JavaScript
- Tailwind CSS响应式框架
- Font Awesome图标库
- WebSocket实时通信

#### 样式特性
- CSS Grid布局系统
- CSS动画和过渡效果
- 自定义CSS变量管理主题
- 媒体查询适配不同屏幕

#### 交互逻辑
- 事件驱动的状态管理
- 异步任务的进度跟踪
- 实时数据更新机制
- 用户操作的反馈系统

### 5. 部署和使用说明

#### 文件结构
```
WAIC_Demo/
├── README.md                 # 项目文档
├── ui-design.html           # 综合UI设计展示页面
├── display-screen.html      # 大屏幕展示界面
└── (其他项目文件)
```

#### 使用方式
1. **开发预览**：直接在浏览器中打开HTML文件
2. **展会部署**：将文件部署到Web服务器
3. **大屏展示**：全屏模式打开display-screen.html
4. **移动端测试**：使用ui-design.html查看整体设计

#### 浏览器兼容性
- Chrome 80+
- Firefox 75+
- Safari 13+
- Edge 80+

### 6. 设计迭代记录

#### v1.0 - 初始设计
- 基础三栏布局
- 手机竖屏视频显示
- 基本状态反馈

#### v1.1 - 优化调整
- 移除系统状态显示
- 标题修改为SSID品牌
- 简化检测结果显示

#### v1.2 - 功能完善
- 新增大模型AI分析输出框
- 优化右侧栏布局
- 移除技术细节显示

#### v1.3 - 最终版本
- 标题优化：深伪检测结果、大模型AI分析
- 内容精简：专注核心功能展示
- 视觉优化：增强科技感和专业性
